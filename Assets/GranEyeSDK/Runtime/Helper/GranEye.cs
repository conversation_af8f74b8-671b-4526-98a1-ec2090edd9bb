
using System.Threading.Tasks;
using com.luxza.granecho.Runtime;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace com.luxza.graneye
{
    public class GranEye
    {


        public enum  GranReactiveAction
        {
            MainCameraView,
            Echo_Connected,
            Echo_Disconnected,
            Auto_Calibration,
        }
        public class GranEyeEntity
        {
            public GranReactiveAction Action;
            public Texture LeftCameraTexture;
        }
        
        
        
        private static GranEye _instance;
        
        public static GranEye Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new GranEye();
                }
                return _instance;
            }
        }
        
        
        
        
        
        
        
        
        private GranEyeCore m_GranEyeCore;
        private EchoCore m_EchoCore;
        
        public string CurrentEchoVersion {get; private set;} = string.Empty;
        public bool IsEchoConnected { get; private set; }= false;

        private GameObject m_Self;
        private GranEye()
        {

            this.m_Self = new GameObject("GranEyeHelper");
            GameObject.DontDestroyOnLoad(m_Self);
            
            this.m_GranEyeCore = new GranEyeCore();
            this.m_EchoCore = new GameObject("GranEcho").AddComponent<EchoCore>();
            this.m_EchoCore.transform.SetParent(m_Self.transform);
            
            this.m_EchoCore.On_EchoConnectionChanged_Callback.AddListener(OnEchoConnectionChanged);
            this.m_EchoCore.On_EchoReceived_Callback.AddListener(OnReceiveEchoData);
        }

        public bool Init()
        {
            this.m_GranEyeCore.Init(m_Self.GetComponent<RectTransform>());

            return true;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<GranEyeEntity> GetMainCameraViewData()
        {
            var t_Ret = await this.m_GranEyeCore.GetImage(GranEyeCore.CameraPos.Left);
            var t_Entity = new GranEyeEntity()
            {
                Action = GranReactiveAction.MainCameraView,
                LeftCameraTexture = t_Ret.Texture
            };
            return t_Entity;
        }
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        private async void OnEchoConnectionChanged(bool success)
        {
            IsEchoConnected = success;
          
            if (success)
            {
                if (string.IsNullOrEmpty(CurrentEchoVersion))
                {
                    await UniTask.Delay(1500);
                    m_EchoCore.GetVersion();
                }
            }
        }

        private void OnReceiveEchoData(string data)
        {
            if (data.Contains("GE"))
            {
                CurrentEchoVersion = data.Replace("GE","");
            }
        }
    }
}